# User-DF 微服务架构文档

## 概述

本文档描述了将原有的 `orc_mongodb_service` 单体架构重构为异步微服务架构的设计和实现。新架构将原有功能分解为三个独立的微服务，提供更好的可扩展性、可维护性和性能。

## 架构设计

### 微服务组件

#### 1. ORC数据处理微服务 (orc_processor_service)
- **端口**: 8001
- **职责**: 
  - 读取和解析ORC文件
  - 数据清洗和验证
  - PID去重和优化
  - Milvus向量查询
  - 将处理结果发送到消息队列

#### 2. MongoDB写入微服务 (mongodb_writer_service)
- **端口**: 8002
- **职责**:
  - 从消息队列接收处理后的数据
  - 批量写入MongoDB数据库
  - 错误处理和重试机制
  - 写入统计和监控

#### 3. 监控服务 (monitoring_service)
- **职责**:
  - 监控所有微服务的健康状态
  - 收集和展示性能统计
  - 提供统一的监控面板
  - 告警和通知功能

### 服务间通信

- **消息队列**: 使用Redis作为消息队列，实现ORC处理服务和MongoDB写入服务之间的异步通信
- **HTTP API**: 监控服务通过HTTP API获取各微服务的状态和统计信息
- **队列名称**: `mongodb_write_queue`

## 目录结构

```
services/
├── orc_processor_service/          # ORC数据处理微服务
│   ├── __init__.py
│   ├── main.py                     # 服务主入口
│   └── service.py                  # 服务实现
├── mongodb_writer_service/         # MongoDB写入微服务
│   ├── __init__.py
│   ├── main.py                     # 服务主入口
│   └── service.py                  # 服务实现
└── monitoring_service/             # 监控服务
    ├── __init__.py
    └── monitor.py                  # 监控实现

configs/
├── orc_processor_service/          # ORC处理服务配置
│   ├── development.yaml
│   └── production.yaml
├── mongodb_writer_service/         # MongoDB写入服务配置
│   ├── development.yaml
│   └── production.yaml
└── monitoring_service/             # 监控服务配置
    ├── development.yaml
    └── production.yaml

scripts/microservices/              # 部署和管理脚本
├── start_all_services.sh          # 启动所有服务
├── stop_all_services.sh           # 停止所有服务
└── status_services.sh             # 检查服务状态
```

## 部署和使用

### 环境要求

- Python 3.8+
- Redis 服务器
- MongoDB 数据库
- Milvus 向量数据库

### 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 确保Redis服务运行
redis-server

# 确保MongoDB服务运行
mongod
```

### 启动服务

#### 方式一：使用脚本启动所有服务

```bash
# 启动所有微服务（开发环境）
./scripts/microservices/start_all_services.sh

# 启动所有微服务（生产环境）
./scripts/microservices/start_all_services.sh --environment production

# 自定义端口启动
./scripts/microservices/start_all_services.sh --orc-port 8001 --mongodb-port 8002
```

#### 方式二：单独启动各个服务

```bash
# 启动ORC处理服务
python3 -m services.orc_processor_service.main --host 0.0.0.0 --port 8001

# 启动MongoDB写入服务
python3 -m services.mongodb_writer_service.main --host 0.0.0.0 --port 8002

# 启动监控服务（实时监控）
python3 -m services.monitoring_service.monitor --mode live
```

### 服务管理

#### 检查服务状态

```bash
# 检查所有服务状态
./scripts/microservices/status_services.sh

# 或者通过HTTP API检查
curl http://localhost:8001/health  # ORC处理服务
curl http://localhost:8002/health  # MongoDB写入服务
```

#### 停止服务

```bash
# 停止所有服务
./scripts/microservices/stop_all_services.sh
```

#### 监控服务

```bash
# 实时监控面板
python3 -m services.monitoring_service.monitor --mode live

# 单次状态检查
python3 -m services.monitoring_service.monitor --mode once
```

### 处理数据

#### 通过API提交处理任务

```bash
# 提交ORC文件处理任务
curl -X POST "http://localhost:8001/process" \
  -H "Content-Type: application/json" \
  -d '{
    "orc_file": "/path/to/orc/file.orc",
    "process_date": "20250722",
    "prov_id": 200
  }'
```

#### 查看任务状态

```bash
# 查看任务状态
curl http://localhost:8001/task/{task_id}

# 查看服务统计
curl http://localhost:8001/stats
curl http://localhost:8002/stats
```

## 配置说明

### ORC处理服务配置

主要配置项：
- `service.port`: 服务端口
- `batch_processing.batch_size`: 用户处理批次大小
- `milvus`: Milvus连接配置
- `redis`: Redis连接和队列配置

### MongoDB写入服务配置

主要配置项：
- `service.port`: 服务端口
- `mongodb`: MongoDB连接配置
- `batch_processing.write_batch_size`: 写入批次大小
- `retry`: 重试配置

### 监控服务配置

主要配置项：
- `services`: 被监控服务的URL配置
- `monitoring.check_interval`: 检查间隔
- `alerts`: 告警配置

## 性能优化

### ORC处理服务优化

1. **批处理优化**: 调整 `batch_size` 和 `pid_query_batch_size`
2. **Milvus连接池**: 配置合适的连接池大小
3. **内存管理**: 启用垃圾回收和内存清理

### MongoDB写入服务优化

1. **批量写入**: 调整 `write_batch_size` 提高写入效率
2. **连接池**: 配置MongoDB连接池参数
3. **写入确认**: 根据需要调整写入确认级别

### 系统级优化

1. **Redis配置**: 调整Redis内存和持久化设置
2. **网络优化**: 确保服务间网络延迟最小
3. **资源监控**: 使用监控服务跟踪资源使用情况

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 验证配置文件格式
   - 查看启动日志

2. **Redis连接失败**
   - 确保Redis服务运行
   - 检查Redis连接配置
   - 验证网络连通性

3. **MongoDB写入失败**
   - 检查MongoDB连接
   - 验证数据库权限
   - 查看错误日志

4. **Milvus查询失败**
   - 确保Milvus服务运行
   - 检查集合是否存在
   - 验证向量维度

### 日志查看

```bash
# 查看服务日志
tail -f logs/orc_processor_service/orc_processor_service.log
tail -f logs/mongodb_writer_service/mongodb_writer_service.log
tail -f logs/monitoring_service/monitoring_service.log

# 查看启动日志
tail -f logs/orc_processor_service_startup.log
tail -f logs/mongodb_writer_service_startup.log
```

## 与原有架构的对比

### 优势

1. **可扩展性**: 各服务可独立扩展
2. **可维护性**: 职责分离，代码更清晰
3. **容错性**: 单个服务故障不影响整体
4. **性能**: 异步处理，提高并发能力
5. **监控**: 统一监控面板，便于运维

### 迁移说明

1. **移除tmux并行处理**: 不再需要多省并行处理逻辑
2. **配置文件分离**: 每个服务有独立的配置文件
3. **部署方式变更**: 从单进程变为多服务部署
4. **监控方式改进**: 从日志监控变为API监控

## 扩展和定制

### 添加新的微服务

1. 创建服务目录和文件
2. 实现服务逻辑和API
3. 添加配置文件
4. 更新部署脚本
5. 集成到监控系统

### 自定义消息队列

可以将Redis替换为其他消息队列系统：
- RabbitMQ
- Apache Kafka
- AWS SQS

### 集成容器化部署

可以为每个微服务创建Docker镜像，实现容器化部署：
- 创建Dockerfile
- 配置docker-compose.yml
- 实现Kubernetes部署

## 快速开始

### 1. 启动所有微服务

```bash
# 启动所有微服务（开发环境）
./scripts/microservices/start_all_services.sh

# 检查服务状态
./scripts/microservices/status_services.sh
```

### 2. 运行演示

```bash
# 运行微服务架构演示
python3 scripts/microservices/demo_microservices.py

# 仅检查服务状态
python3 scripts/microservices/demo_microservices.py --check-only
```

### 3. 运行测试

```bash
# 运行集成测试
python3 scripts/microservices/test_microservices.py
```

### 4. 启动监控

```bash
# 启动实时监控面板
python3 -m services.monitoring_service.monitor --mode live
```

### 5. 停止服务

```bash
# 停止所有微服务
./scripts/microservices/stop_all_services.sh
```

## 总结

新的微服务架构提供了更好的可扩展性、可维护性和性能。通过将原有的单体应用分解为独立的微服务，系统变得更加灵活和健壮。配合完善的监控和部署工具，可以更好地支持大规模数据处理需求。

### 主要改进

1. **架构解耦**: 将ORC处理和MongoDB写入分离为独立服务
2. **异步通信**: 使用Redis消息队列实现服务间异步通信
3. **独立扩展**: 每个服务可以根据需要独立扩展
4. **统一监控**: 提供统一的监控面板和健康检查
5. **简化部署**: 提供完整的部署脚本和管理工具
6. **移除tmux**: 完全移除多省并行处理的tmux逻辑

### 性能提升

- **并发处理**: 支持更高的并发处理能力
- **资源优化**: 更好的资源利用和内存管理
- **故障隔离**: 单个服务故障不影响整体系统
- **水平扩展**: 支持水平扩展以处理更大负载
