#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB写入微服务

异步微服务，负责从消息队列接收数据并批量写入MongoDB
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import redis.asyncio as redis
from fastapi import FastAPI, HTTPException
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import Config<PERSON>anager, Logger, ExceptionHandler
from shared.database.mongodb import MongoDBPool, MongoDBOperations
from shared.utils import DataProcessor, TimeUtils


@dataclass
class WriteTask:
    """写入任务数据结构"""
    task_id: str
    user_data: List[Dict[str, Any]]
    process_date: str
    prov_id: int
    received_at: float
    status: str = "pending"
    retry_count: int = 0


class MongoDBWriterService:
    """MongoDB写入微服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_service_config("mongodb_writer_service")
        self.logger = Logger(__name__, config_manager)
        self.exception_handler = ExceptionHandler(self.logger)
        
        # 初始化组件
        self.mongodb_pool = None
        self.redis_client = None
        
        # 服务状态
        self.is_running = False
        self.current_tasks = {}
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_users_written": 0,
            "total_inserts": 0,
            "total_updates": 0,
            "total_skips": 0,
            "start_time": time.time()
        }
        
        # 创建FastAPI应用
        self.app = FastAPI(title="MongoDB Writer Service", version="1.0.0")
        self._setup_routes()
        
        # 队列处理任务
        self.queue_task = None
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化MongoDB写入微服务...")
            
            # 初始化MongoDB连接池
            self.mongodb_pool = MongoDBPool(self.config_manager)
            await self.mongodb_pool.initialize()
            
            # 初始化Redis连接
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            self.is_running = True
            
            # 启动队列处理任务
            self.queue_task = asyncio.create_task(self._process_queue())
            
            self.logger.info("MongoDB写入微服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭服务"""
        try:
            self.logger.info("关闭MongoDB写入微服务...")
            self.is_running = False
            
            # 停止队列处理任务
            if self.queue_task:
                self.queue_task.cancel()
                try:
                    await self.queue_task
                except asyncio.CancelledError:
                    pass
            
            if self.redis_client:
                await self.redis_client.close()
            
            if self.mongodb_pool:
                await self.mongodb_pool.close()
            
            self.logger.info("MongoDB写入微服务已关闭")
            
        except Exception as e:
            self.logger.error(f"服务关闭失败: {e}")
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy" if self.is_running else "unhealthy",
                "service": "mongodb_writer_service",
                "version": "1.0.0",
                "uptime": time.time() - self.stats["start_time"],
                "stats": self.stats
            }
        
        @self.app.get("/stats")
        async def get_stats():
            """获取统计信息"""
            return {
                "stats": self.stats,
                "current_tasks": len(self.current_tasks),
                "task_details": list(self.current_tasks.keys())
            }
        
        @self.app.get("/queue/status")
        async def get_queue_status():
            """获取队列状态"""
            try:
                queue_name = self.config.get("redis", {}).get("queue_name", "mongodb_write_queue")
                queue_length = await self.redis_client.llen(queue_name)
                
                return {
                    "queue_name": queue_name,
                    "queue_length": queue_length,
                    "is_processing": self.queue_task is not None and not self.queue_task.done()
                }
            except Exception as e:
                return {"error": str(e)}
    
    async def _process_queue(self):
        """处理Redis队列中的任务"""
        queue_name = self.config.get("redis", {}).get("queue_name", "mongodb_write_queue")
        batch_size = self.config.get("batch_processing", {}).get("batch_size", 10)
        
        self.logger.info(f"开始处理队列: {queue_name}")
        
        while self.is_running:
            try:
                # 批量获取任务
                tasks = []
                for _ in range(batch_size):
                    task_data = await self.redis_client.brpop(queue_name, timeout=1)
                    if task_data:
                        tasks.append(task_data[1])
                    else:
                        break
                
                if tasks:
                    await self._process_batch_tasks(tasks)
                else:
                    # 没有任务时短暂休眠
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                self.logger.error(f"队列处理错误: {e}")
                await asyncio.sleep(1)
    
    async def _process_batch_tasks(self, task_data_list: List[str]):
        """批量处理任务"""
        try:
            write_tasks = []
            
            # 解析任务数据
            for task_data in task_data_list:
                try:
                    result_dict = json.loads(task_data)
                    
                    task = WriteTask(
                        task_id=result_dict["task_id"],
                        user_data=result_dict["user_data"],
                        process_date=result_dict["process_date"],
                        prov_id=result_dict["prov_id"],
                        received_at=time.time()
                    )
                    
                    write_tasks.append(task)
                    self.current_tasks[task.task_id] = task
                    
                except Exception as e:
                    self.logger.error(f"解析任务数据失败: {e}")
                    continue
            
            if not write_tasks:
                return
            
            self.logger.info(f"开始批量处理 {len(write_tasks)} 个写入任务")
            
            # 批量写入MongoDB
            await self._batch_write_to_mongodb(write_tasks)
            
        except Exception as e:
            self.logger.error(f"批量任务处理失败: {e}")
    
    async def _batch_write_to_mongodb(self, write_tasks: List[WriteTask]):
        """批量写入MongoDB"""
        try:
            mongodb_ops = MongoDBOperations(
                self.mongodb_pool, 
                self.config.get("mongodb", {}).get("collection", "user_pid_records_optimized")
            )
            
            for task in write_tasks:
                try:
                    task.status = "processing"
                    self.stats["total_tasks"] += 1
                    
                    # 执行写入操作
                    result = await self._write_user_data(mongodb_ops, task.user_data)
                    
                    # 更新统计信息
                    self.stats["completed_tasks"] += 1
                    self.stats["total_users_written"] += len(task.user_data)
                    self.stats["total_inserts"] += result.get("inserts", 0)
                    self.stats["total_updates"] += result.get("updates", 0)
                    self.stats["total_skips"] += result.get("skips", 0)
                    
                    task.status = "completed"
                    
                    self.logger.debug(f"任务完成: {task.task_id}, 写入用户数: {len(task.user_data)}")
                    
                except Exception as e:
                    self.logger.error(f"任务写入失败: {task.task_id}, 错误: {e}")
                    task.status = "failed"
                    task.retry_count += 1
                    self.stats["failed_tasks"] += 1
                    
                    # 重试机制
                    max_retries = self.config.get("retry", {}).get("max_retries", 3)
                    if task.retry_count < max_retries:
                        await self._retry_task(task)
            
            # 清理完成的任务
            await self._cleanup_completed_tasks(write_tasks)
            
        except Exception as e:
            self.logger.error(f"批量写入MongoDB失败: {e}")
    
    async def _write_user_data(self, mongodb_ops: MongoDBOperations, user_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """写入用户数据到MongoDB"""
        try:
            # 使用现有的MongoDB操作类进行写入
            # 这里需要适配为异步操作
            
            inserts = 0
            updates = 0
            skips = 0
            
            batch_size = self.config.get("batch_processing", {}).get("write_batch_size", 1000)
            
            for i in range(0, len(user_data), batch_size):
                batch_data = user_data[i:i + batch_size]
                
                # 执行批量upsert操作
                result = await mongodb_ops.bulk_upsert_async(batch_data)
                
                inserts += result.get("inserted_count", 0)
                updates += result.get("modified_count", 0)
                skips += result.get("matched_count", 0) - result.get("modified_count", 0)
            
            return {
                "inserts": inserts,
                "updates": updates,
                "skips": skips
            }
            
        except Exception as e:
            self.logger.error(f"用户数据写入失败: {e}")
            raise
    
    async def _retry_task(self, task: WriteTask):
        """重试失败的任务"""
        try:
            retry_delay = self.config.get("retry", {}).get("delay", 5)
            await asyncio.sleep(retry_delay)
            
            # 将任务重新放入队列
            queue_name = self.config.get("redis", {}).get("queue_name", "mongodb_write_queue")
            
            result_dict = {
                "task_id": task.task_id,
                "user_data": task.user_data,
                "process_date": task.process_date,
                "prov_id": task.prov_id,
                "processed_at": time.time(),
                "stats": {"retry_count": task.retry_count}
            }
            
            result_json = json.dumps(result_dict, ensure_ascii=False)
            await self.redis_client.lpush(queue_name, result_json)
            
            self.logger.info(f"任务重试: {task.task_id}, 重试次数: {task.retry_count}")
            
        except Exception as e:
            self.logger.error(f"任务重试失败: {task.task_id}, 错误: {e}")
    
    async def _cleanup_completed_tasks(self, tasks: List[WriteTask]):
        """清理完成的任务"""
        try:
            cleanup_delay = self.config.get("cleanup", {}).get("delay", 300)  # 5分钟
            
            async def cleanup_after_delay():
                await asyncio.sleep(cleanup_delay)
                for task in tasks:
                    if task.task_id in self.current_tasks:
                        del self.current_tasks[task.task_id]
            
            asyncio.create_task(cleanup_after_delay())
            
        except Exception as e:
            self.logger.error(f"任务清理失败: {e}")
    
    def run(self, host: str = "0.0.0.0", port: int = 8002):
        """运行服务"""
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )


async def create_service():
    """创建服务实例"""
    config_manager = ConfigManager()
    service = MongoDBWriterService(config_manager)
    await service.initialize()
    return service


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="MongoDB写入微服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8002, help="服务端口")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 设置配置文件路径
    if args.config:
        os.environ['USER_DF_CONFIG_FILE'] = args.config
    
    # 创建并运行服务
    async def main():
        service = await create_service()
        try:
            service.run(host=args.host, port=args.port)
        finally:
            await service.shutdown()
    
    asyncio.run(main())
