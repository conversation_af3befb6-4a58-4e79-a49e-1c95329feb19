#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC数据处理微服务

异步微服务，负责ORC文件处理和数据发送到消息队列
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import redis.asyncio as redis
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigMana<PERSON>, <PERSON><PERSON>, ExceptionHandler
from shared.database.milvus import MilvusPool, MilvusVectorOperations
from shared.utils import DataProcessor, TimeUtils
from services.orc_mongodb_service.processors import ORCDataProcessor
from services.orc_mongodb_service.orc_reader import SimpleORCReader


@dataclass
class ProcessingTask:
    """处理任务数据结构"""
    task_id: str
    orc_file: str
    process_date: str
    prov_id: int
    created_at: float
    status: str = "pending"


@dataclass
class ProcessingResult:
    """处理结果数据结构"""
    task_id: str
    user_data: List[Dict[str, Any]]
    process_date: str
    prov_id: int
    processed_at: float
    stats: Dict[str, Any]


class ProcessTaskRequest(BaseModel):
    """处理任务请求模型"""
    orc_file: str
    process_date: str
    prov_id: int


class ORCProcessorService:
    """ORC数据处理微服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_service_config("orc_processor_service")
        self.logger = Logger(__name__, config_manager)
        self.exception_handler = ExceptionHandler(self.logger)
        
        # 初始化组件
        self.orc_reader = SimpleORCReader(config_manager)
        self.orc_processor = ORCDataProcessor(config_manager)
        self.milvus_pool = None
        self.redis_client = None
        
        # 服务状态
        self.is_running = False
        self.current_tasks = {}
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_users_processed": 0,
            "total_files_processed": 0,
            "start_time": time.time()
        }
        
        # 创建FastAPI应用
        self.app = FastAPI(title="ORC Processor Service", version="1.0.0")
        self._setup_routes()
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化ORC数据处理微服务...")
            
            # 初始化Milvus连接池
            self.milvus_pool = MilvusPool(self.config_manager)
            await self.milvus_pool.initialize()
            
            # 初始化Redis连接
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            self.is_running = True
            self.logger.info("ORC数据处理微服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭服务"""
        try:
            self.logger.info("关闭ORC数据处理微服务...")
            self.is_running = False
            
            if self.redis_client:
                await self.redis_client.close()
            
            if self.milvus_pool:
                await self.milvus_pool.close()
            
            self.logger.info("ORC数据处理微服务已关闭")
            
        except Exception as e:
            self.logger.error(f"服务关闭失败: {e}")
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy" if self.is_running else "unhealthy",
                "service": "orc_processor_service",
                "version": "1.0.0",
                "uptime": time.time() - self.stats["start_time"],
                "stats": self.stats
            }
        
        @self.app.get("/stats")
        async def get_stats():
            """获取统计信息"""
            return {
                "stats": self.stats,
                "current_tasks": len(self.current_tasks),
                "task_details": list(self.current_tasks.keys())
            }
        
        @self.app.post("/process")
        async def process_file(request: ProcessTaskRequest, background_tasks: BackgroundTasks):
            """处理ORC文件"""
            task_id = f"task_{int(time.time() * 1000)}"
            
            task = ProcessingTask(
                task_id=task_id,
                orc_file=request.orc_file,
                process_date=request.process_date,
                prov_id=request.prov_id,
                created_at=time.time()
            )
            
            self.current_tasks[task_id] = task
            background_tasks.add_task(self._process_file_async, task)
            
            return {
                "task_id": task_id,
                "status": "accepted",
                "message": "任务已接受，开始处理"
            }
        
        @self.app.get("/task/{task_id}")
        async def get_task_status(task_id: str):
            """获取任务状态"""
            if task_id not in self.current_tasks:
                raise HTTPException(status_code=404, detail="任务不存在")
            
            task = self.current_tasks[task_id]
            return asdict(task)
    
    async def _process_file_async(self, task: ProcessingTask):
        """异步处理文件"""
        try:
            self.logger.info(f"开始处理任务: {task.task_id}, 文件: {task.orc_file}")
            task.status = "processing"
            self.stats["total_tasks"] += 1
            
            # 处理ORC文件
            user_data_list = await self._process_orc_file(task.orc_file, task.process_date, task.prov_id)
            
            # 创建处理结果
            result = ProcessingResult(
                task_id=task.task_id,
                user_data=user_data_list,
                process_date=task.process_date,
                prov_id=task.prov_id,
                processed_at=time.time(),
                stats={
                    "total_users": len(user_data_list),
                    "processing_time": time.time() - task.created_at
                }
            )
            
            # 发送到Redis队列
            await self._send_to_queue(result)
            
            task.status = "completed"
            self.stats["completed_tasks"] += 1
            self.stats["total_users_processed"] += len(user_data_list)
            self.stats["total_files_processed"] += 1
            
            self.logger.info(f"任务完成: {task.task_id}, 处理用户数: {len(user_data_list)}")
            
        except Exception as e:
            self.logger.error(f"任务处理失败: {task.task_id}, 错误: {e}")
            task.status = "failed"
            self.stats["failed_tasks"] += 1
        
        finally:
            # 清理完成的任务（保留一段时间用于查询）
            await asyncio.sleep(300)  # 5分钟后清理
            if task.task_id in self.current_tasks:
                del self.current_tasks[task.task_id]
    
    async def _process_orc_file(self, orc_file: str, process_date: str, prov_id: int) -> List[Dict[str, Any]]:
        """处理ORC文件"""
        try:
            # 使用现有的ORC处理器处理文件
            # 注意：这里需要适配为异步处理
            user_data_list = self.orc_processor.process_orc_file_chunked(
                orc_file, process_date, prov_id
            )
            
            return user_data_list
            
        except Exception as e:
            self.logger.error(f"ORC文件处理失败: {orc_file}, 错误: {e}")
            raise
    
    async def _send_to_queue(self, result: ProcessingResult):
        """发送处理结果到Redis队列"""
        try:
            queue_name = self.config.get("redis", {}).get("queue_name", "mongodb_write_queue")
            
            # 序列化结果
            result_json = json.dumps(asdict(result), ensure_ascii=False)
            
            # 发送到Redis队列
            await self.redis_client.lpush(queue_name, result_json)
            
            self.logger.debug(f"结果已发送到队列: {queue_name}, 任务ID: {result.task_id}")
            
        except Exception as e:
            self.logger.error(f"发送结果到队列失败: {e}")
            raise
    
    def run(self, host: str = "0.0.0.0", port: int = 8001):
        """运行服务"""
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )


async def create_service():
    """创建服务实例"""
    config_manager = ConfigManager()
    service = ORCProcessorService(config_manager)
    await service.initialize()
    return service


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="ORC数据处理微服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8001, help="服务端口")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 设置配置文件路径
    if args.config:
        os.environ['USER_DF_CONFIG_FILE'] = args.config
    
    # 创建并运行服务
    async def main():
        service = await create_service()
        try:
            service.run(host=args.host, port=args.port)
        finally:
            await service.shutdown()
    
    asyncio.run(main())
