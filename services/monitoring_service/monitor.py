#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控服务

监控ORC处理和MongoDB写入微服务的状态和性能
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import aiohttp
import redis.asyncio as redis
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.panel import Panel
from rich.layout import Layout
from rich.text import Text

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger


@dataclass
class ServiceStatus:
    """服务状态数据结构"""
    name: str
    url: str
    status: str
    uptime: float
    stats: Dict[str, Any]
    last_check: float
    error_message: Optional[str] = None


class MonitoringService:
    """监控服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_service_config("monitoring_service")
        self.logger = Logger(__name__, config_manager)
        
        # 服务配置
        self.services = {
            "orc_processor": {
                "name": "ORC处理服务",
                "url": self.config.get("services", {}).get("orc_processor", {}).get("url", "http://localhost:8001"),
                "health_endpoint": "/health",
                "stats_endpoint": "/stats"
            },
            "mongodb_writer": {
                "name": "MongoDB写入服务", 
                "url": self.config.get("services", {}).get("mongodb_writer", {}).get("url", "http://localhost:8002"),
                "health_endpoint": "/health",
                "stats_endpoint": "/stats"
            }
        }
        
        # 监控状态
        self.service_status = {}
        self.is_running = False
        self.console = Console()
        self.redis_client = None
        
        # 监控统计
        self.monitor_stats = {
            "start_time": time.time(),
            "total_checks": 0,
            "failed_checks": 0,
            "alerts_sent": 0
        }
    
    async def initialize(self):
        """初始化监控服务"""
        try:
            self.logger.info("初始化监控服务...")
            
            # 初始化Redis连接（用于获取队列状态）
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            self.is_running = True
            self.logger.info("监控服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"监控服务初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭监控服务"""
        try:
            self.logger.info("关闭监控服务...")
            self.is_running = False
            
            if self.redis_client:
                await self.redis_client.close()
            
            self.logger.info("监控服务已关闭")
            
        except Exception as e:
            self.logger.error(f"监控服务关闭失败: {e}")
    
    async def check_service_health(self, service_key: str, service_config: Dict[str, Any]) -> ServiceStatus:
        """检查单个服务的健康状态"""
        try:
            url = service_config["url"] + service_config["health_endpoint"]
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        return ServiceStatus(
                            name=service_config["name"],
                            url=service_config["url"],
                            status=data.get("status", "unknown"),
                            uptime=data.get("uptime", 0),
                            stats=data.get("stats", {}),
                            last_check=time.time()
                        )
                    else:
                        return ServiceStatus(
                            name=service_config["name"],
                            url=service_config["url"],
                            status="unhealthy",
                            uptime=0,
                            stats={},
                            last_check=time.time(),
                            error_message=f"HTTP {response.status}"
                        )
        
        except Exception as e:
            return ServiceStatus(
                name=service_config["name"],
                url=service_config["url"],
                status="error",
                uptime=0,
                stats={},
                last_check=time.time(),
                error_message=str(e)
            )
    
    async def get_service_stats(self, service_key: str, service_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取服务详细统计信息"""
        try:
            url = service_config["url"] + service_config["stats_endpoint"]
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"HTTP {response.status}"}
        
        except Exception as e:
            return {"error": str(e)}
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取Redis队列状态"""
        try:
            queue_name = self.config.get("redis", {}).get("queue_name", "mongodb_write_queue")
            queue_length = await self.redis_client.llen(queue_name)
            
            return {
                "queue_name": queue_name,
                "queue_length": queue_length,
                "status": "healthy" if queue_length >= 0 else "error"
            }
        
        except Exception as e:
            return {
                "queue_name": "unknown",
                "queue_length": -1,
                "status": "error",
                "error": str(e)
            }
    
    async def collect_all_stats(self) -> Dict[str, Any]:
        """收集所有服务的统计信息"""
        all_stats = {
            "timestamp": time.time(),
            "services": {},
            "queue": {},
            "monitor": self.monitor_stats
        }
        
        # 检查所有服务
        for service_key, service_config in self.services.items():
            # 健康检查
            health_status = await self.check_service_health(service_key, service_config)
            self.service_status[service_key] = health_status
            
            # 详细统计
            stats = await self.get_service_stats(service_key, service_config)
            
            all_stats["services"][service_key] = {
                "health": asdict(health_status),
                "stats": stats
            }
            
            self.monitor_stats["total_checks"] += 1
            if health_status.status != "healthy":
                self.monitor_stats["failed_checks"] += 1
        
        # 队列状态
        all_stats["queue"] = await self.get_queue_status()
        
        return all_stats
    
    def create_monitoring_display(self, stats: Dict[str, Any]) -> Layout:
        """创建监控显示界面"""
        layout = Layout()
        
        # 创建主要布局
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        # 头部信息
        header_text = Text("User-DF 微服务监控面板", style="bold blue")
        header_text.append(f" - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", style="dim")
        layout["header"].update(Panel(header_text, title="监控状态"))
        
        # 主体分为左右两部分
        layout["body"].split_row(
            Layout(name="services", ratio=2),
            Layout(name="details", ratio=1)
        )
        
        # 服务状态表格
        services_table = Table(title="服务状态")
        services_table.add_column("服务名称", style="cyan")
        services_table.add_column("状态", style="green")
        services_table.add_column("运行时间", style="yellow")
        services_table.add_column("处理任务", style="blue")
        services_table.add_column("错误信息", style="red")
        
        for service_key, service_data in stats["services"].items():
            health = service_data["health"]
            service_stats = service_data["stats"].get("stats", {})
            
            status_color = "green" if health["status"] == "healthy" else "red"
            uptime_str = self._format_uptime(health["uptime"])
            
            tasks_info = f"{service_stats.get('completed_tasks', 0)}/{service_stats.get('total_tasks', 0)}"
            error_msg = health.get("error_message", "") or ""
            
            services_table.add_row(
                health["name"],
                f"[{status_color}]{health['status']}[/{status_color}]",
                uptime_str,
                tasks_info,
                error_msg[:30] + "..." if len(error_msg) > 30 else error_msg
            )
        
        layout["services"].update(Panel(services_table, title="服务状态"))
        
        # 详细信息
        details_text = []
        
        # 队列信息
        queue_info = stats["queue"]
        details_text.append(f"队列状态: {queue_info.get('status', 'unknown')}")
        details_text.append(f"队列长度: {queue_info.get('queue_length', 0)}")
        details_text.append("")
        
        # 总体统计
        total_completed = sum(
            service_data["stats"].get("stats", {}).get("completed_tasks", 0)
            for service_data in stats["services"].values()
        )
        total_failed = sum(
            service_data["stats"].get("stats", {}).get("failed_tasks", 0)
            for service_data in stats["services"].values()
        )
        
        details_text.append(f"总完成任务: {total_completed}")
        details_text.append(f"总失败任务: {total_failed}")
        details_text.append(f"监控检查: {self.monitor_stats['total_checks']}")
        details_text.append(f"检查失败: {self.monitor_stats['failed_checks']}")
        
        details_panel = Panel("\n".join(details_text), title="详细信息")
        layout["details"].update(details_panel)
        
        # 底部信息
        footer_text = f"监控运行时间: {self._format_uptime(time.time() - self.monitor_stats['start_time'])}"
        layout["footer"].update(Panel(footer_text, style="dim"))
        
        return layout
    
    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        if seconds < 60:
            return f"{int(seconds)}秒"
        elif seconds < 3600:
            return f"{int(seconds // 60)}分{int(seconds % 60)}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}时{minutes}分"
    
    async def run_monitoring_loop(self):
        """运行监控循环"""
        check_interval = self.config.get("monitoring", {}).get("check_interval", 5)
        
        with Live(console=self.console, refresh_per_second=1) as live:
            while self.is_running:
                try:
                    # 收集统计信息
                    stats = await self.collect_all_stats()
                    
                    # 更新显示
                    display = self.create_monitoring_display(stats)
                    live.update(display)
                    
                    # 等待下次检查
                    await asyncio.sleep(check_interval)
                    
                except KeyboardInterrupt:
                    self.logger.info("接收到中断信号，停止监控")
                    break
                except Exception as e:
                    self.logger.error(f"监控循环错误: {e}")
                    await asyncio.sleep(1)
    
    async def run_once(self) -> Dict[str, Any]:
        """运行一次监控检查"""
        return await self.collect_all_stats()
    
    def print_stats_summary(self, stats: Dict[str, Any]):
        """打印统计摘要"""
        self.console.print("\n=== 微服务状态摘要 ===", style="bold blue")
        
        for service_key, service_data in stats["services"].items():
            health = service_data["health"]
            service_stats = service_data["stats"].get("stats", {})
            
            status_style = "green" if health["status"] == "healthy" else "red"
            
            self.console.print(f"\n[cyan]{health['name']}[/cyan]:")
            self.console.print(f"  状态: [{status_style}]{health['status']}[/{status_style}]")
            self.console.print(f"  运行时间: {self._format_uptime(health['uptime'])}")
            self.console.print(f"  完成任务: {service_stats.get('completed_tasks', 0)}")
            self.console.print(f"  失败任务: {service_stats.get('failed_tasks', 0)}")
            
            if health.get("error_message"):
                self.console.print(f"  错误: [red]{health['error_message']}[/red]")
        
        # 队列状态
        queue_info = stats["queue"]
        self.console.print(f"\n[cyan]消息队列[/cyan]:")
        self.console.print(f"  状态: {queue_info.get('status', 'unknown')}")
        self.console.print(f"  队列长度: {queue_info.get('queue_length', 0)}")


async def create_monitoring_service():
    """创建监控服务实例"""
    config_manager = ConfigManager()
    service = MonitoringService(config_manager)
    await service.initialize()
    return service


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="微服务监控服务")
    parser.add_argument("--mode", choices=["live", "once"], default="live", 
                       help="监控模式: live=实时监控, once=单次检查")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 设置配置文件路径
    if args.config:
        os.environ['USER_DF_CONFIG_FILE'] = args.config
    
    async def main():
        service = await create_monitoring_service()
        
        try:
            if args.mode == "live":
                await service.run_monitoring_loop()
            else:
                stats = await service.run_once()
                service.print_stats_summary(stats)
        finally:
            await service.shutdown()
    
    asyncio.run(main())
