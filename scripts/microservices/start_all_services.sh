#!/bin/bash
# -*- coding: utf-8 -*-
"""
启动所有微服务的脚本

启动ORC处理微服务、MongoDB写入微服务和监控服务
"""

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 默认配置
ENVIRONMENT="development"
CONFIG_DIR="$PROJECT_ROOT/configs"
LOG_DIR="$PROJECT_ROOT/logs"

# 服务配置
ORC_PROCESSOR_PORT=8001
MONGODB_WRITER_PORT=8002
MONITORING_PORT=8003

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -c|--config-dir)
            CONFIG_DIR="$2"
            shift 2
            ;;
        --orc-port)
            ORC_PROCESSOR_PORT="$2"
            shift 2
            ;;
        --mongodb-port)
            MONGODB_WRITER_PORT="$2"
            shift 2
            ;;
        --monitor-port)
            MONITORING_PORT="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  -e, --environment ENV     运行环境 (development|production)"
            echo "  -c, --config-dir DIR      配置目录路径"
            echo "  --orc-port PORT          ORC处理服务端口 (默认: 8001)"
            echo "  --mongodb-port PORT      MongoDB写入服务端口 (默认: 8002)"
            echo "  --monitor-port PORT      监控服务端口 (默认: 8003)"
            echo "  -h, --help               显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    log_info "Python版本: $(python3 --version)"
}

# 检查依赖
check_dependencies() {
    log_info "检查项目依赖..."
    
    cd "$PROJECT_ROOT"
    
    # 检查requirements.txt
    if [[ -f "requirements.txt" ]]; then
        log_info "安装Python依赖..."
        pip3 install -r requirements.txt
    fi
    
    # 检查Redis连接
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            log_info "Redis连接正常"
        else
            log_warn "Redis连接失败，请确保Redis服务正在运行"
        fi
    else
        log_warn "Redis CLI未安装，无法检查Redis连接"
    fi
}

# 创建日志目录
create_log_dirs() {
    log_info "创建日志目录..."
    
    mkdir -p "$LOG_DIR/orc_processor_service"
    mkdir -p "$LOG_DIR/mongodb_writer_service"
    mkdir -p "$LOG_DIR/monitoring_service"
    
    log_info "日志目录创建完成"
}

# 启动单个服务
start_service() {
    local service_name=$1
    local service_path=$2
    local port=$3
    local config_file=$4
    
    log_info "启动 $service_name..."
    
    cd "$PROJECT_ROOT"
    
    # 构建启动命令
    local cmd="python3 -m $service_path --host 0.0.0.0 --port $port --environment $ENVIRONMENT"
    
    if [[ -n "$config_file" && -f "$config_file" ]]; then
        cmd="$cmd --config $config_file"
    fi
    
    # 后台启动服务
    nohup $cmd > "$LOG_DIR/${service_name}_startup.log" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$LOG_DIR/${service_name}.pid"
    
    log_info "$service_name 已启动，PID: $pid"
    
    # 等待服务启动
    sleep 2
    
    # 检查服务是否正常启动
    if kill -0 $pid 2>/dev/null; then
        log_info "$service_name 启动成功"
    else
        log_error "$service_name 启动失败"
        return 1
    fi
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local port=$2
    local max_retries=10
    local retry_count=0
    
    log_info "检查 $service_name 健康状态..."
    
    while [[ $retry_count -lt $max_retries ]]; do
        if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
            log_info "$service_name 健康检查通过"
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        log_debug "等待 $service_name 启动... ($retry_count/$max_retries)"
        sleep 2
    done
    
    log_error "$service_name 健康检查失败"
    return 1
}

# 主函数
main() {
    log_info "=== 启动User-DF微服务集群 ==="
    log_info "环境: $ENVIRONMENT"
    log_info "配置目录: $CONFIG_DIR"
    
    # 检查环境
    check_python
    check_dependencies
    create_log_dirs
    
    # 配置文件路径
    local orc_config="$CONFIG_DIR/orc_processor_service/$ENVIRONMENT.yaml"
    local mongodb_config="$CONFIG_DIR/mongodb_writer_service/$ENVIRONMENT.yaml"
    local monitor_config="$CONFIG_DIR/monitoring_service/$ENVIRONMENT.yaml"
    
    # 启动服务
    log_info "开始启动微服务..."
    
    # 1. 启动ORC处理服务
    if ! start_service "orc_processor_service" "services.orc_processor_service.main" $ORC_PROCESSOR_PORT "$orc_config"; then
        log_error "ORC处理服务启动失败"
        exit 1
    fi
    
    # 2. 启动MongoDB写入服务
    if ! start_service "mongodb_writer_service" "services.mongodb_writer_service.main" $MONGODB_WRITER_PORT "$mongodb_config"; then
        log_error "MongoDB写入服务启动失败"
        exit 1
    fi
    
    # 等待服务完全启动
    sleep 5
    
    # 健康检查
    log_info "执行健康检查..."
    
    if ! check_service_health "ORC处理服务" $ORC_PROCESSOR_PORT; then
        log_error "ORC处理服务健康检查失败"
        exit 1
    fi
    
    if ! check_service_health "MongoDB写入服务" $MONGODB_WRITER_PORT; then
        log_error "MongoDB写入服务健康检查失败"
        exit 1
    fi
    
    # 显示服务状态
    log_info "=== 服务启动完成 ==="
    log_info "ORC处理服务: http://localhost:$ORC_PROCESSOR_PORT"
    log_info "MongoDB写入服务: http://localhost:$MONGODB_WRITER_PORT"
    log_info ""
    log_info "服务管理命令:"
    log_info "  查看状态: $SCRIPT_DIR/status_services.sh"
    log_info "  停止服务: $SCRIPT_DIR/stop_all_services.sh"
    log_info "  启动监控: python3 -m services.monitoring_service.monitor --mode live"
    log_info ""
    log_info "日志文件位置: $LOG_DIR"
}

# 信号处理
trap 'log_info "接收到中断信号，正在清理..."; exit 1' INT TERM

# 执行主函数
main "$@"
